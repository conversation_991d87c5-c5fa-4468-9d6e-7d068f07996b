# 🧪 Master-Know 前端完整测试指令

## 📋 任务概述

您需要将当前的模拟前端功能替换为真实的API集成，并使用Playwright进行端到端测试验证。

## 🎯 测试目标

1. **API集成测试** - 替换模拟数据，使用真实后端API
2. **功能验证测试** - 使用Playwright验证所有用户交互
3. **端到端测试** - 完整的用户流程测试

## 🚀 第一阶段：API集成测试

### 1.1 环境准备

```bash
# 确保后端服务运行
cd backend
source venv/bin/activate
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 确保前端服务运行
cd frontend
npm run dev
```

### 1.2 重新生成API客户端

```bash
cd frontend

# 获取最新OpenAPI规范
curl -o openapi.json http://localhost:8000/api/v1/openapi.json

# 重新生成客户端（修复之前的问题）
npm run generate-client

# 如果生成失败，手动安装依赖
npm install @hey-api/client-axios --legacy-peer-deps
```

### 1.3 API连接测试脚本

创建 `frontend/test_real_api.py`：

```python
#!/usr/bin/env python3
"""
真实API连接测试
测试所有前端需要的API端点
"""

import requests
import json
import sys

class APITester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api/v1"
        self.token = None
        
    def test_health_check(self):
        """测试健康检查"""
        response = requests.get(f"{self.base_url}/utils/health-check")
        assert response.status_code == 200
        print("✅ 健康检查通过")
        
    def test_user_registration(self):
        """测试用户注册"""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = requests.post(f"{self.base_url}/users/signup", json=user_data)
        print(f"注册响应: {response.status_code}")
        return response.status_code in [200, 201, 409]  # 409表示用户已存在
        
    def test_user_login(self):
        """测试用户登录"""
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
        response = requests.post(f"{self.base_url}/login/access-token", data=login_data)
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            print("✅ 登录成功")
            return True
        print(f"❌ 登录失败: {response.status_code}")
        return False
        
    def get_headers(self):
        """获取认证头"""
        return {"Authorization": f"Bearer {self.token}"} if self.token else {}
        
    def test_conversations_api(self):
        """测试对话API"""
        headers = self.get_headers()
        
        # 创建新对话
        conv_data = {"title": "测试对话"}
        response = requests.post(f"{self.base_url}/conversations/", 
                               json=conv_data, headers=headers)
        if response.status_code != 200:
            print(f"❌ 创建对话失败: {response.status_code}")
            return False
            
        conv_id = response.json()["id"]
        print(f"✅ 创建对话成功: {conv_id}")
        
        # 发送消息
        chat_data = {
            "conversation_id": conv_id,
            "message": "你好，这是一个测试消息"
        }
        response = requests.post(f"{self.base_url}/conversations/chat",
                               json=chat_data, headers=headers)
        if response.status_code == 200:
            print("✅ 发送消息成功")
            return True
        print(f"❌ 发送消息失败: {response.status_code}")
        return False
        
    def test_search_api(self):
        """测试搜索API"""
        headers = self.get_headers()
        
        search_data = {
            "query": "测试搜索",
            "search_type": "text",
            "limit": 5
        }
        response = requests.post(f"{self.base_url}/search/documents",
                               json=search_data, headers=headers)
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 搜索成功，返回 {len(results.get('results', []))} 个结果")
            return True
        print(f"❌ 搜索失败: {response.status_code}")
        return False
        
    def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始真实API测试...")
        
        try:
            self.test_health_check()
            self.test_user_registration()
            
            if self.test_user_login():
                self.test_conversations_api()
                self.test_search_api()
                print("🎉 所有API测试完成！")
                return True
            else:
                print("❌ 登录失败，无法继续测试")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            return False

if __name__ == "__main__":
    tester = APITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
```

### 1.4 执行API测试

```bash
cd frontend
python3 test_real_api.py
```

## 🔧 第二阶段：前端API集成

### 2.1 更新聊天页面

修改 `frontend/src/routes/chat.tsx`，替换模拟API调用：

```typescript
// 替换模拟的API调用为真实的客户端调用
import { ConversationsService } from '../client'

// 在组件中使用真实API
const { data: conversations, isLoading: conversationsLoading } = useQuery({
  queryKey: ['conversations'],
  queryFn: () => ConversationsService.readConversations({ limit: 50 }),
})

const sendMessage = useMutation({
  mutationFn: (messageText: string) => {
    if (!currentConversationId) {
      throw new Error('No conversation selected')
    }
    return ConversationsService.chatWithAi({
      requestBody: {
        conversation_id: currentConversationId,
        message: messageText,
      },
    })
  },
  // ... 其他配置
})
```

### 2.2 更新搜索页面

修改 `frontend/src/routes/search.tsx`，使用真实搜索API：

```typescript
import { SearchService } from '../client'

const searchMutation = useMutation({
  mutationFn: async (searchQuery: string) => {
    return SearchService.searchDocuments({
      requestBody: {
        query: searchQuery,
        search_type: searchType,
        limit: 20,
        offset: 0,
      },
    })
  },
  // ... 其他配置
})
```

### 2.3 添加认证支持

创建 `frontend/src/hooks/useAuth.ts`：

```typescript
import { useState, useEffect } from 'react'
import { LoginService } from '../client'

export const useAuth = () => {
  const [token, setToken] = useState<string | null>(
    localStorage.getItem('access_token')
  )
  
  const login = async (email: string, password: string) => {
    const response = await LoginService.loginAccessToken({
      requestBody: { username: email, password }
    })
    const accessToken = response.access_token
    setToken(accessToken)
    localStorage.setItem('access_token', accessToken)
    return accessToken
  }
  
  const logout = () => {
    setToken(null)
    localStorage.removeItem('access_token')
  }
  
  return { token, login, logout, isAuthenticated: !!token }
}
```

## 🎭 第三阶段：Playwright端到端测试

### 3.1 安装Playwright

```bash
cd frontend
npm install -D @playwright/test
npx playwright install
```

### 3.2 创建Playwright配置

创建 `frontend/playwright.config.ts`：

```typescript
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
```

### 3.3 创建测试用例

创建 `frontend/tests/homepage.spec.ts`：

```typescript
import { test, expect } from '@playwright/test'

test('首页功能测试', async ({ page }) => {
  await page.goto('/')
  
  // 检查页面标题
  await expect(page.locator('h1')).toContainText('欢迎使用 Master-Know')
  
  // 检查快速操作卡片
  await expect(page.locator('text=智能对话')).toBeVisible()
  await expect(page.locator('text=文档搜索')).toBeVisible()
  
  // 测试导航到聊天页面
  await page.click('text=智能对话')
  await expect(page).toHaveURL('/chat')
})
```

创建 `frontend/tests/chat.spec.ts`：

```typescript
import { test, expect } from '@playwright/test'

test('聊天功能测试', async ({ page }) => {
  await page.goto('/chat')
  
  // 检查聊天界面元素
  await expect(page.locator('text=对话')).toBeVisible()
  await expect(page.locator('text=新对话')).toBeVisible()
  
  // 测试创建新对话
  await page.click('text=新对话')
  
  // 等待对话创建完成
  await page.waitForTimeout(1000)
  
  // 测试发送消息
  const messageInput = page.locator('input[placeholder="输入消息..."]')
  await messageInput.fill('这是一个测试消息')
  await page.click('text=发送')
  
  // 验证消息发送
  await expect(page.locator('text=这是一个测试消息')).toBeVisible()
})
```

创建 `frontend/tests/search.spec.ts`：

```typescript
import { test, expect } from '@playwright/test'

test('搜索功能测试', async ({ page }) => {
  await page.goto('/search')
  
  // 检查搜索界面
  await expect(page.locator('text=文档搜索')).toBeVisible()
  
  // 测试搜索功能
  const searchInput = page.locator('input[placeholder="输入搜索关键词..."]')
  await searchInput.fill('测试搜索')
  
  // 选择搜索类型
  await page.selectOption('select', 'text')
  
  // 执行搜索
  await page.click('text=搜索')
  
  // 等待搜索结果
  await page.waitForTimeout(2000)
  
  // 验证搜索结果或无结果提示
  const hasResults = await page.locator('text=搜索结果').isVisible()
  const noResults = await page.locator('text=没有找到相关结果').isVisible()
  
  expect(hasResults || noResults).toBeTruthy()
})
```

### 3.4 执行Playwright测试

```bash
# 运行所有测试
npx playwright test

# 运行特定测试
npx playwright test homepage.spec.ts

# 以调试模式运行
npx playwright test --debug

# 生成测试报告
npx playwright show-report
```

## 📊 第四阶段：完整验证

### 4.1 创建完整测试脚本

创建 `frontend/complete_test.py`：

```python
#!/usr/bin/env python3
"""
完整的前端测试验证脚本
包含API测试和Playwright测试
"""

import subprocess
import sys
import time

def run_api_tests():
    """运行API测试"""
    print("🔧 运行API集成测试...")
    result = subprocess.run([sys.executable, "test_real_api.py"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ API测试通过")
        return True
    else:
        print(f"❌ API测试失败: {result.stderr}")
        return False

def run_playwright_tests():
    """运行Playwright测试"""
    print("🎭 运行Playwright端到端测试...")
    result = subprocess.run(["npx", "playwright", "test"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Playwright测试通过")
        return True
    else:
        print(f"❌ Playwright测试失败: {result.stderr}")
        return False

def main():
    print("🚀 开始完整的前端测试验证...")
    
    # 运行API测试
    api_success = run_api_tests()
    
    # 运行Playwright测试
    playwright_success = run_playwright_tests()
    
    if api_success and playwright_success:
        print("🎉 所有测试通过！前端功能完全验证成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 4.2 执行完整测试

```bash
cd frontend
python3 complete_test.py
```

## 🎯 成功标准

完成所有测试后，您应该能够：

1. ✅ **API集成** - 所有前端功能使用真实后端API
2. ✅ **用户认证** - 登录/注册功能正常工作
3. ✅ **聊天功能** - 真实的AI对话交互
4. ✅ **搜索功能** - 真实的文档搜索
5. ✅ **端到端测试** - Playwright测试全部通过
6. ✅ **错误处理** - 网络错误和API错误的优雅处理

## 📝 注意事项

1. **认证令牌** - 确保API调用包含正确的认证头
2. **错误处理** - 添加适当的错误边界和用户反馈
3. **加载状态** - 确保所有异步操作有加载指示器
4. **数据验证** - 验证API响应数据的完整性
5. **性能优化** - 检查API调用的性能和缓存策略

## 🚀 完成后的交付物

- [ ] 完全集成真实API的前端应用
- [ ] 通过所有Playwright测试的测试报告
- [ ] API集成测试报告
- [ ] 用户使用文档
- [ ] 部署就绪的生产构建

祝您测试顺利！🎉
