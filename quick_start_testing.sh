#!/bin/bash

# 🚀 Master-Know 前端测试快速启动脚本
# 为下一位同事准备的一键启动脚本

set -e

echo "🚀 Master-Know 前端测试环境快速启动"
echo "========================================"

# 检查当前目录
if [ ! -f "COMPLETE_TESTING_INSTRUCTIONS.md" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 函数：检查服务是否运行
check_service() {
    local url=$1
    local name=$2
    
    if curl -s "$url" > /dev/null 2>&1; then
        echo "✅ $name 正在运行"
        return 0
    else
        echo "❌ $name 未运行"
        return 1
    fi
}

# 函数：启动后端服务
start_backend() {
    echo "🔧 检查后端服务..."
    
    if check_service "http://localhost:8000/api/v1/utils/health-check" "后端API"; then
        return 0
    fi
    
    echo "📦 启动后端服务..."
    if [ -d "backend" ]; then
        cd backend
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
            echo "🐍 虚拟环境已激活"
        else
            echo "⚠️  未找到虚拟环境，请手动激活"
        fi
        
        # 后台启动后端
        nohup python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > backend.log 2>&1 &
        echo $! > backend.pid
        echo "🚀 后端服务启动中..."
        
        # 等待服务启动
        for i in {1..30}; do
            if check_service "http://localhost:8000/api/v1/utils/health-check" "后端API"; then
                break
            fi
            echo "⏳ 等待后端启动... ($i/30)"
            sleep 2
        done
        
        cd ..
    else
        echo "❌ 未找到backend目录"
        exit 1
    fi
}

# 函数：启动前端服务
start_frontend() {
    echo "🔧 检查前端服务..."
    
    if check_service "http://localhost:3000" "前端应用"; then
        return 0
    fi
    
    echo "📦 启动前端服务..."
    if [ -d "frontend" ]; then
        cd frontend
        
        # 检查依赖
        if [ ! -d "node_modules" ]; then
            echo "📦 安装前端依赖..."
            npm install --legacy-peer-deps
        fi
        
        # 后台启动前端
        nohup npm run dev > frontend.log 2>&1 &
        echo $! > frontend.pid
        echo "🚀 前端服务启动中..."
        
        # 等待服务启动
        for i in {1..30}; do
            if check_service "http://localhost:3000" "前端应用"; then
                break
            fi
            echo "⏳ 等待前端启动... ($i/30)"
            sleep 2
        done
        
        cd ..
    else
        echo "❌ 未找到frontend目录"
        exit 1
    fi
}

# 函数：创建测试文件
create_test_files() {
    echo "📝 创建测试文件..."
    
    cd frontend
    
    # 创建API测试文件
    cat > test_real_api.py << 'EOF'
#!/usr/bin/env python3
"""
真实API连接测试
"""

import requests
import json
import sys

class APITester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api/v1"
        self.token = None
        
    def test_health_check(self):
        """测试健康检查"""
        response = requests.get(f"{self.base_url}/utils/health-check")
        assert response.status_code == 200
        print("✅ 健康检查通过")
        
    def test_user_registration(self):
        """测试用户注册"""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = requests.post(f"{self.base_url}/users/signup", json=user_data)
        print(f"注册响应: {response.status_code}")
        return response.status_code in [200, 201, 409]
        
    def test_user_login(self):
        """测试用户登录"""
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
        response = requests.post(f"{self.base_url}/login/access-token", data=login_data)
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            print("✅ 登录成功")
            return True
        print(f"❌ 登录失败: {response.status_code}")
        return False
        
    def get_headers(self):
        """获取认证头"""
        return {"Authorization": f"Bearer {self.token}"} if self.token else {}
        
    def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始真实API测试...")
        
        try:
            self.test_health_check()
            self.test_user_registration()
            
            if self.test_user_login():
                print("🎉 基础API测试完成！")
                return True
            else:
                print("❌ 登录失败，无法继续测试")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            return False

if __name__ == "__main__":
    tester = APITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
EOF

    chmod +x test_real_api.py
    
    cd ..
    echo "✅ 测试文件创建完成"
}

# 函数：运行初始测试
run_initial_tests() {
    echo "🧪 运行初始API测试..."
    
    cd frontend
    python3 test_real_api.py
    cd ..
    
    echo "✅ 初始测试完成"
}

# 函数：显示下一步指令
show_next_steps() {
    echo ""
    echo "🎉 环境准备完成！"
    echo "===================="
    echo ""
    echo "📋 服务状态:"
    echo "  🌐 前端应用: http://localhost:3000"
    echo "  🔧 后端API: http://localhost:8000"
    echo "  📚 API文档: http://localhost:8000/docs"
    echo ""
    echo "📝 下一步操作:"
    echo "  1. 阅读完整指令: cat COMPLETE_TESTING_INSTRUCTIONS.md"
    echo "  2. 重新生成API客户端: cd frontend && npm run generate-client"
    echo "  3. 集成真实API到前端组件"
    echo "  4. 安装Playwright: cd frontend && npm install -D @playwright/test"
    echo "  5. 创建并运行端到端测试"
    echo ""
    echo "🛠️  停止服务:"
    echo "  - 停止后端: kill \$(cat backend/backend.pid) 2>/dev/null || true"
    echo "  - 停止前端: kill \$(cat frontend/frontend.pid) 2>/dev/null || true"
    echo ""
    echo "📖 详细文档: COMPLETE_TESTING_INSTRUCTIONS.md"
}

# 主执行流程
main() {
    echo "开始环境准备..."
    
    # 启动后端服务
    start_backend
    
    # 启动前端服务  
    start_frontend
    
    # 创建测试文件
    create_test_files
    
    # 运行初始测试
    run_initial_tests
    
    # 显示下一步指令
    show_next_steps
}

# 错误处理
trap 'echo "❌ 脚本执行失败"; exit 1' ERR

# 执行主函数
main

echo "✅ 快速启动完成！"
